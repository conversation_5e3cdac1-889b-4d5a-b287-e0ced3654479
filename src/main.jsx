import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import * as serviceWorker from "./serviceWorker";
import App from "./";
import "./index.css";
import { Provider } from "react-redux";
import store from "./redux/store";
import { PlatformProvider } from "./helpers/context/PlatformContext";

const root = ReactDOM.createRoot(document.getElementById("root"));

root.render(
  <BrowserRouter>
    <PlatformProvider>
      <Provider store={store}>
        <App />
        <ToastContainer />
      </Provider>
    </PlatformProvider>
  </BrowserRouter>
);

serviceWorker.unregister();
